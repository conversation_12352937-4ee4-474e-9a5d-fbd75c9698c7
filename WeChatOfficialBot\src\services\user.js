/**
 * 用户管理服务
 * 处理微信公众号用户相关功能
 */

const Logger = require('../utils/logger');
const { WECHAT_API, ERROR_CODES } = require('../config/wechat');

class UserService {
  constructor(wechatService) {
    this.wechatService = wechatService;
  }

  /**
   * 获取用户列表
   * @param {string} nextOpenId - 下一个用户的OpenID（用于分页）
   * @returns {Promise<Object>} 用户列表数据
   */
  async getUserList(nextOpenId = '') {
    try {
      const accessToken = await this.wechatService.getAccessToken();
      let url = `${WECHAT_API.USER_LIST_URL}?access_token=${accessToken}`;
      
      if (nextOpenId) {
        url += `&next_openid=${nextOpenId}`;
      }

      Logger.debug('获取用户列表', { nextOpenId });

      const response = await fetch(url);
      const data = await response.json();

      if (data.errcode && data.errcode !== 0) {
        throw new Error(`获取用户列表失败: ${data.errmsg} (${data.errcode})`);
      }

      Logger.user('system', '获取用户列表成功', {
        total: data.total,
        count: data.count,
        hasNext: !!data.next_openid
      });

      return {
        total: data.total || 0,
        count: data.count || 0,
        openIds: data.data?.openid || [],
        nextOpenId: data.next_openid || null
      };

    } catch (error) {
      Logger.error('获取用户列表失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取所有用户列表（自动分页）
   * @returns {Promise<Array>} 所有用户OpenID列表
   */
  async getAllUsers() {
    try {
      const allUsers = [];
      let nextOpenId = '';
      let hasMore = true;

      Logger.info('开始获取所有用户列表');

      while (hasMore) {
        const result = await this.getUserList(nextOpenId);
        
        if (result.openIds && result.openIds.length > 0) {
          allUsers.push(...result.openIds);
        }

        if (result.nextOpenId) {
          nextOpenId = result.nextOpenId;
        } else {
          hasMore = false;
        }

        // 避免请求过于频繁
        if (hasMore) {
          await this.sleep(100);
        }
      }

      Logger.user('system', '获取所有用户完成', { totalUsers: allUsers.length });
      return allUsers;

    } catch (error) {
      Logger.error('获取所有用户失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取用户详细信息
   * @param {string} openId - 用户OpenID
   * @returns {Promise<Object>} 用户详细信息
   */
  async getUserInfo(openId) {
    try {
      const accessToken = await this.wechatService.getAccessToken();
      const url = `${WECHAT_API.USER_INFO_URL}?access_token=${accessToken}&openid=${openId}&lang=zh_CN`;

      Logger.debug('获取用户信息', { openId: openId.substring(0, 8) + '...' });

      const response = await fetch(url);
      const data = await response.json();

      if (data.errcode && data.errcode !== 0) {
        throw new Error(`获取用户信息失败: ${data.errmsg} (${data.errcode})`);
      }

      const userInfo = {
        openId: data.openid,
        nickname: data.nickname || '',
        sex: data.sex || 0, // 0-未知, 1-男, 2-女
        province: data.province || '',
        city: data.city || '',
        country: data.country || '',
        headImgUrl: data.headimgurl || '',
        subscribeTime: data.subscribe_time || 0,
        unionId: data.unionid || '',
        remark: data.remark || '',
        groupId: data.groupid || 0,
        tagIdList: data.tagid_list || [],
        subscribeScene: data.subscribe_scene || '',
        qrScene: data.qr_scene || 0,
        qrSceneStr: data.qr_scene_str || ''
      };

      Logger.user(openId, '获取用户信息成功', {
        nickname: userInfo.nickname,
        subscribeTime: userInfo.subscribeTime
      });

      return userInfo;

    } catch (error) {
      Logger.error('获取用户信息失败', { 
        openId: openId.substring(0, 8) + '...',
        error: error.message 
      });
      throw error;
    }
  }

  /**
   * 批量获取用户信息
   * @param {Array} openIds - 用户OpenID列表
   * @param {number} batchSize - 批次大小
   * @returns {Promise<Array>} 用户信息列表
   */
  async getBatchUserInfo(openIds, batchSize = 10) {
    try {
      const userInfoList = [];
      
      Logger.info('开始批量获取用户信息', { 
        totalUsers: openIds.length,
        batchSize 
      });

      for (let i = 0; i < openIds.length; i += batchSize) {
        const batch = openIds.slice(i, i + batchSize);
        const batchPromises = batch.map(openId => 
          this.getUserInfo(openId).catch(error => {
            Logger.warn('获取单个用户信息失败', { 
              openId: openId.substring(0, 8) + '...',
              error: error.message 
            });
            return null;
          })
        );

        const batchResults = await Promise.all(batchPromises);
        const validResults = batchResults.filter(result => result !== null);
        userInfoList.push(...validResults);

        Logger.debug('批次处理完成', { 
          batch: Math.floor(i / batchSize) + 1,
          processed: validResults.length,
          total: userInfoList.length 
        });

        // 避免请求过于频繁
        if (i + batchSize < openIds.length) {
          await this.sleep(200);
        }
      }

      Logger.user('system', '批量获取用户信息完成', { 
        totalProcessed: userInfoList.length,
        totalRequested: openIds.length 
      });

      return userInfoList;

    } catch (error) {
      Logger.error('批量获取用户信息失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 获取用户统计信息
   * @returns {Promise<Object>} 用户统计数据
   */
  async getUserStats() {
    try {
      const userList = await this.getUserList();
      
      const stats = {
        totalUsers: userList.total,
        currentBatchCount: userList.count,
        hasMoreUsers: !!userList.nextOpenId,
        timestamp: new Date().toISOString()
      };

      Logger.user('system', '获取用户统计', stats);
      return stats;

    } catch (error) {
      Logger.error('获取用户统计失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 搜索用户
   * @param {string} keyword - 搜索关键词（昵称）
   * @param {number} limit - 返回数量限制
   * @returns {Promise<Array>} 匹配的用户列表
   */
  async searchUsers(keyword, limit = 50) {
    try {
      Logger.info('搜索用户', { keyword, limit });

      // 获取所有用户OpenID
      const allOpenIds = await this.getAllUsers();
      
      if (allOpenIds.length === 0) {
        return [];
      }

      // 批量获取用户信息
      const allUsers = await this.getBatchUserInfo(allOpenIds);
      
      // 搜索匹配的用户
      const matchedUsers = allUsers.filter(user => 
        user && user.nickname && 
        user.nickname.toLowerCase().includes(keyword.toLowerCase())
      ).slice(0, limit);

      Logger.user('system', '用户搜索完成', { 
        keyword,
        totalUsers: allUsers.length,
        matchedUsers: matchedUsers.length 
      });

      return matchedUsers;

    } catch (error) {
      Logger.error('搜索用户失败', { error: error.message });
      throw error;
    }
  }

  /**
   * 检查用户是否关注
   * @param {string} openId - 用户OpenID
   * @returns {Promise<boolean>} 是否关注
   */
  async isUserSubscribed(openId) {
    try {
      const userInfo = await this.getUserInfo(openId);
      return userInfo && userInfo.subscribeTime > 0;
    } catch (error) {
      // 如果用户未关注，API会返回错误
      if (error.message.includes('40013') || error.message.includes('40003')) {
        return false;
      }
      throw error;
    }
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 格式化用户信息用于显示
   * @param {Object} userInfo - 用户信息
   * @returns {Object} 格式化后的用户信息
   */
  formatUserInfo(userInfo) {
    return {
      openId: userInfo.openId.substring(0, 8) + '...',
      nickname: userInfo.nickname || '未设置',
      sex: ['未知', '男', '女'][userInfo.sex] || '未知',
      location: [userInfo.country, userInfo.province, userInfo.city]
        .filter(Boolean).join(' ') || '未知',
      subscribeTime: userInfo.subscribeTime ? 
        new Date(userInfo.subscribeTime * 1000).toLocaleString('zh-CN') : '未知',
      headImgUrl: userInfo.headImgUrl || ''
    };
  }
}

module.exports = UserService;
