/**
 * 微信API测试脚本
 * 测试微信公众号API调用功能
 */

const config = require('../src/config');
const WeChatService = require('../src/services/wechat');

async function testWeChatAPI() {
  console.log('🧪 微信API测试\n');

  // 显示配置信息
  console.log('📋 当前配置:');
  console.log(`   AppID: ${config.wechat.appId}`);
  console.log(`   AppSecret: ${config.wechat.appSecret ? '已配置' : '未配置'}`);
  console.log(`   Token: ${config.wechat.token}\n`);

  try {
    const wechatService = new WeChatService();

    // 测试获取access_token
    console.log('🔑 测试获取Access Token...');
    const accessToken = await wechatService.getAccessToken();
    console.log(`✅ Access Token获取成功: ${accessToken.substring(0, 20)}...\n`);

    // 测试获取用户列表
    console.log('👥 测试获取用户列表...');
    const url = `https://api.weixin.qq.com/cgi-bin/user/get?access_token=${accessToken}`;
    
    const response = await fetch(url);
    const userData = await response.json();
    
    if (userData.errcode && userData.errcode !== 0) {
      console.log(`❌ 获取用户列表失败: ${userData.errmsg} (${userData.errcode})`);
      
      // 分析错误码
      switch (userData.errcode) {
        case 40001:
          console.log('   原因: AppSecret错误或access_token无效');
          break;
        case 40013:
          console.log('   原因: 无效的AppID');
          break;
        case 48001:
          console.log('   原因: API功能未授权，请检查公众号类型和权限');
          break;
        case 89503:
          console.log('   原因: 此IP调用需要管理员确认，请联系管理员');
          break;
        default:
          console.log(`   错误码: ${userData.errcode}`);
      }
    } else {
      console.log('✅ 用户列表获取成功');
      console.log(`   关注用户总数: ${userData.total || 0}`);
      console.log(`   本次返回数量: ${userData.count || 0}`);
      
      if (userData.data && userData.data.openid) {
        console.log(`   示例OpenID: ${userData.data.openid[0]?.substring(0, 8)}...`);
      }
    }

  } catch (error) {
    console.log(`❌ API测试失败: ${error.message}`);
    
    if (error.message.includes('fetch')) {
      console.log('   可能的原因: 网络连接问题');
    } else if (error.message.includes('40001')) {
      console.log('   可能的原因: AppSecret配置错误');
    }
  }

  console.log('\n📝 说明:');
  console.log('1. 如果出现48001错误，说明当前公众号类型不支持此API');
  console.log('2. 个人订阅号通常没有获取用户列表的权限');
  console.log('3. 需要认证的服务号才有完整的API权限');
  console.log('4. 测试号可以使用大部分API功能');
  
  console.log('\n🔗 获取测试号:');
  console.log('   访问: https://mp.weixin.qq.com/debug/cgi-bin/sandbox?t=sandbox/login');
  console.log('   使用测试号可以完整测试所有功能');
}

// 运行测试
testWeChatAPI().catch(console.error);
