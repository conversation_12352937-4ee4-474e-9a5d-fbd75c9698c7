/**
 * 用户管理控制器
 * 处理用户相关的HTTP请求
 */

const UserService = require('../services/user');
const Logger = require('../utils/logger');

class UserController {
  constructor(wechatService) {
    this.userService = new UserService(wechatService);
  }

  /**
   * 获取用户列表
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async getUserList(req, res) {
    try {
      const { next_openid, page_size = 10000 } = req.query;
      
      Logger.api(req.method, req.originalUrl, 'processing', 0);
      const startTime = Date.now();

      const result = await this.userService.getUserList(next_openid);
      
      const duration = Date.now() - startTime;
      Logger.api(req.method, req.originalUrl, 200, duration);

      res.json({
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Logger.error('获取用户列表API失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 获取所有用户
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async getAllUsers(req, res) {
    try {
      Logger.api(req.method, req.originalUrl, 'processing', 0);
      const startTime = Date.now();

      const users = await this.userService.getAllUsers();
      
      const duration = Date.now() - startTime;
      Logger.api(req.method, req.originalUrl, 200, duration);

      res.json({
        success: true,
        data: {
          total: users.length,
          openIds: users
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Logger.error('获取所有用户API失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 获取用户详细信息
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async getUserInfo(req, res) {
    try {
      const { openid } = req.params;
      
      if (!openid) {
        return res.status(400).json({
          success: false,
          error: '缺少openid参数',
          timestamp: new Date().toISOString()
        });
      }

      Logger.api(req.method, req.originalUrl, 'processing', 0);
      const startTime = Date.now();

      const userInfo = await this.userService.getUserInfo(openid);
      const formattedInfo = this.userService.formatUserInfo(userInfo);
      
      const duration = Date.now() - startTime;
      Logger.api(req.method, req.originalUrl, 200, duration);

      res.json({
        success: true,
        data: {
          raw: userInfo,
          formatted: formattedInfo
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Logger.error('获取用户信息API失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 批量获取用户信息
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async getBatchUserInfo(req, res) {
    try {
      const { openids, batch_size = 10, format = false } = req.body;
      
      if (!openids || !Array.isArray(openids)) {
        return res.status(400).json({
          success: false,
          error: 'openids必须是数组',
          timestamp: new Date().toISOString()
        });
      }

      Logger.api(req.method, req.originalUrl, 'processing', 0);
      const startTime = Date.now();

      const userInfoList = await this.userService.getBatchUserInfo(openids, batch_size);
      
      const result = format ? 
        userInfoList.map(user => this.userService.formatUserInfo(user)) :
        userInfoList;
      
      const duration = Date.now() - startTime;
      Logger.api(req.method, req.originalUrl, 200, duration);

      res.json({
        success: true,
        data: {
          total: result.length,
          users: result
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Logger.error('批量获取用户信息API失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 获取用户统计信息
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async getUserStats(req, res) {
    try {
      Logger.api(req.method, req.originalUrl, 'processing', 0);
      const startTime = Date.now();

      const stats = await this.userService.getUserStats();
      
      const duration = Date.now() - startTime;
      Logger.api(req.method, req.originalUrl, 200, duration);

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Logger.error('获取用户统计API失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 搜索用户
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async searchUsers(req, res) {
    try {
      const { keyword, limit = 50 } = req.query;
      
      if (!keyword) {
        return res.status(400).json({
          success: false,
          error: '缺少搜索关键词',
          timestamp: new Date().toISOString()
        });
      }

      Logger.api(req.method, req.originalUrl, 'processing', 0);
      const startTime = Date.now();

      const users = await this.userService.searchUsers(keyword, parseInt(limit));
      const formattedUsers = users.map(user => this.userService.formatUserInfo(user));
      
      const duration = Date.now() - startTime;
      Logger.api(req.method, req.originalUrl, 200, duration);

      res.json({
        success: true,
        data: {
          keyword,
          total: formattedUsers.length,
          users: formattedUsers
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Logger.error('搜索用户API失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 检查用户关注状态
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async checkSubscription(req, res) {
    try {
      const { openid } = req.params;
      
      if (!openid) {
        return res.status(400).json({
          success: false,
          error: '缺少openid参数',
          timestamp: new Date().toISOString()
        });
      }

      Logger.api(req.method, req.originalUrl, 'processing', 0);
      const startTime = Date.now();

      const isSubscribed = await this.userService.isUserSubscribed(openid);
      
      const duration = Date.now() - startTime;
      Logger.api(req.method, req.originalUrl, 200, duration);

      res.json({
        success: true,
        data: {
          openId: openid.substring(0, 8) + '...',
          isSubscribed,
          checkTime: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Logger.error('检查用户关注状态API失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * 获取用户管理面板数据
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async getDashboard(req, res) {
    try {
      Logger.api(req.method, req.originalUrl, 'processing', 0);
      const startTime = Date.now();

      // 获取基础统计
      const stats = await this.userService.getUserStats();
      
      // 获取最近的用户列表（前10个）
      const recentUsers = await this.userService.getUserList();
      const recentUserInfos = [];
      
      if (recentUsers.openIds && recentUsers.openIds.length > 0) {
        const sampleOpenIds = recentUsers.openIds.slice(0, 5);
        const sampleInfos = await this.userService.getBatchUserInfo(sampleOpenIds);
        recentUserInfos.push(...sampleInfos.map(user => this.userService.formatUserInfo(user)));
      }
      
      const duration = Date.now() - startTime;
      Logger.api(req.method, req.originalUrl, 200, duration);

      res.json({
        success: true,
        data: {
          stats,
          recentUsers: recentUserInfos,
          lastUpdate: new Date().toISOString()
        },
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      Logger.error('获取用户面板数据API失败', { error: error.message });
      res.status(500).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = UserController;
