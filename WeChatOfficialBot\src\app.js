/**
 * 微信公众号开发项目主应用
 * 基于微信官方开发文档标准开发
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');

const config = require('./config');
const Logger = require('./utils/logger');
const WeChatController = require('./controllers/wechat');
const UserController = require('./controllers/user');

class WeChatApp {
  constructor() {
    this.app = express();
    this.wechatController = new WeChatController();
    this.userController = new UserController(this.wechatController.wechatService);

    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * 设置中间件
   */
  setupMiddleware() {
    // 安全中间件
    this.app.use(helmet({
      contentSecurityPolicy: false // 微信回调可能需要禁用CSP
    }));

    // CORS配置
    this.app.use(cors({
      origin: config.security.corsOrigin,
      credentials: true
    }));

    // 请求日志
    if (config.server.env !== 'test') {
      this.app.use(morgan('combined', {
        stream: {
          write: (message) => Logger.info(message.trim())
        }
      }));
    }

    // 信任代理（用于获取真实IP）
    this.app.set('trust proxy', true);

    // 解析原始请求体（微信消息是XML格式）
    this.app.use('/wechat', express.raw({ 
      type: 'text/xml',
      limit: '10mb'
    }));

    // 解析JSON请求体（其他API接口）
    this.app.use(express.json({ limit: '1mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '1mb' }));

    // 请求超时设置
    this.app.use((req, res, next) => {
      res.setTimeout(config.wechat.messageTimeout, () => {
        Logger.warn('请求超时', { 
          url: req.url, 
          method: req.method,
          ip: req.ip 
        });
        if (!res.headersSent) {
          res.status(408).send('请求超时');
        }
      });
      next();
    });

    // 添加请求ID（用于日志追踪）
    this.app.use((req, res, next) => {
      req.id = Date.now().toString(36) + Math.random().toString(36).substr(2);
      res.set('X-Request-ID', req.id);
      next();
    });

    Logger.info('中间件设置完成');
  }

  /**
   * 设置路由
   */
  setupRoutes() {
    // 健康检查接口
    this.app.get('/health', (req, res) => {
      this.wechatController.health(req, res);
    });

    // 微信验证接口（GET）
    this.app.get('/wechat', (req, res) => {
      this.wechatController.verify(req, res);
    });

    // 微信消息接收接口（POST）
    this.app.post('/wechat', (req, res) => {
      // 将原始Buffer转换为字符串
      if (Buffer.isBuffer(req.body)) {
        req.body = req.body.toString('utf8');
      }
      this.wechatController.handleMessage(req, res);
    });

    // 用户管理API接口
    this.app.get('/api/users', (req, res) => {
      this.userController.getUserList(req, res);
    });

    this.app.get('/api/users/all', (req, res) => {
      this.userController.getAllUsers(req, res);
    });

    this.app.get('/api/users/stats', (req, res) => {
      this.userController.getUserStats(req, res);
    });

    this.app.get('/api/users/dashboard', (req, res) => {
      this.userController.getDashboard(req, res);
    });

    this.app.get('/api/users/search', (req, res) => {
      this.userController.searchUsers(req, res);
    });

    this.app.get('/api/users/:openid', (req, res) => {
      this.userController.getUserInfo(req, res);
    });

    this.app.get('/api/users/:openid/subscription', (req, res) => {
      this.userController.checkSubscription(req, res);
    });

    this.app.post('/api/users/batch', (req, res) => {
      this.userController.getBatchUserInfo(req, res);
    });

    // 开发调试接口（仅开发环境）
    if (config.server.env === 'development') {
      this.app.get('/wechat/config', (req, res) => {
        this.wechatController.getConfig(req, res);
      });

      this.app.get('/wechat/test', (req, res) => {
        this.wechatController.test(req, res);
      });
    }

    // 根路径
    this.app.get('/', (req, res) => {
      res.json({
        name: 'WeChat Official Bot',
        version: '1.0.0',
        description: '微信公众号开发项目',
        status: 'running',
        timestamp: new Date().toISOString(),
        env: config.server.env
      });
    });

    // 404处理
    this.app.use('*', (req, res) => {
      Logger.warn('404 - 路由不存在', { 
        url: req.originalUrl, 
        method: req.method,
        ip: req.ip 
      });
      res.status(404).json({
        error: '路由不存在',
        path: req.originalUrl,
        method: req.method
      });
    });

    Logger.info('路由设置完成');
  }

  /**
   * 设置错误处理
   */
  setupErrorHandling() {
    // 全局错误处理中间件
    this.app.use((error, req, res, next) => {
      Logger.error('全局错误处理', {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      });

      // 避免重复发送响应
      if (res.headersSent) {
        return next(error);
      }

      // 根据错误类型返回不同的状态码
      let statusCode = 500;
      let message = '服务器内部错误';

      if (error.name === 'ValidationError') {
        statusCode = 400;
        message = '请求参数错误';
      } else if (error.name === 'UnauthorizedError') {
        statusCode = 401;
        message = '未授权访问';
      } else if (error.name === 'ForbiddenError') {
        statusCode = 403;
        message = '禁止访问';
      }

      res.status(statusCode).json({
        error: message,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });
    });

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      Logger.error('未捕获的异常', {
        error: error.message,
        stack: error.stack
      });
      
      // 优雅关闭
      this.gracefulShutdown('uncaughtException');
    });

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      Logger.error('未处理的Promise拒绝', {
        reason: reason,
        promise: promise
      });
      
      // 优雅关闭
      this.gracefulShutdown('unhandledRejection');
    });

    Logger.info('错误处理设置完成');
  }

  /**
   * 启动服务器
   */
  start() {
    const port = config.server.port;
    const host = config.server.host;

    this.server = this.app.listen(port, host, () => {
      Logger.startup('WeChat Official Bot', {
        port,
        host,
        env: config.server.env,
        pid: process.pid
      });

      console.log(`
🚀 微信公众号开发服务启动成功！

📋 服务信息:
   端口: ${port}
   环境: ${config.server.env}
   进程ID: ${process.pid}

🔗 接口地址:
   健康检查: http://localhost:${port}/health
   微信验证: http://localhost:${port}/wechat
   
📚 开发文档: https://developers.weixin.qq.com/doc/subscription/guide/dev/

🎯 下一步: 配置微信公众平台服务器URL
      `);
    });

    // 优雅关闭处理
    process.on('SIGTERM', () => this.gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => this.gracefulShutdown('SIGINT'));

    return this.server;
  }

  /**
   * 优雅关闭服务器
   * @param {string} signal - 关闭信号
   */
  gracefulShutdown(signal) {
    Logger.shutdown('WeChat Official Bot', signal);

    if (this.server) {
      this.server.close(() => {
        Logger.info('服务器已关闭');
        process.exit(0);
      });

      // 强制关闭超时
      setTimeout(() => {
        Logger.error('强制关闭服务器');
        process.exit(1);
      }, 10000);
    } else {
      process.exit(0);
    }
  }
}

// 启动应用
if (require.main === module) {
  const app = new WeChatApp();
  app.start();
}

module.exports = WeChatApp;
