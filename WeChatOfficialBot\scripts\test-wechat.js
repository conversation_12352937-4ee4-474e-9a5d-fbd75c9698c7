/**
 * 微信公众号配置测试脚本
 * 验证微信服务器配置和消息处理功能
 */

const CryptoUtil = require('../src/utils/crypto');
const config = require('../src/config');

console.log('🔧 微信公众号配置测试\n');

// 显示当前配置
console.log('📋 当前配置:');
console.log(`   Token: ${config.wechat.token}`);
console.log(`   AppID: ${config.wechat.appId}`);
console.log(`   AppSecret: ${config.wechat.appSecret ? '已配置' : '未配置'}`);
console.log(`   EncodingAESKey: ${config.wechat.encodingAESKey ? '已配置' : '未配置'}`);
console.log(`   端口: ${config.server.port}\n`);

// 验证配置完整性
try {
  const required = ['token', 'appId'];
  const missing = required.filter(key => !config.wechat[key]);
  
  if (missing.length > 0) {
    console.log('❌ 配置验证失败');
    console.log(`   缺少配置: ${missing.join(', ')}\n`);
    process.exit(1);
  } else {
    console.log('✅ 配置验证通过\n');
  }
} catch (error) {
  console.log('❌ 配置验证异常:', error.message);
  process.exit(1);
}

// 测试签名验证功能
console.log('🧪 测试签名验证功能:');

const testCases = [
  {
    name: '正确签名测试',
    timestamp: '1234567890',
    nonce: 'test_nonce',
    generateCorrectSignature: true
  },
  {
    name: '错误签名测试',
    timestamp: '1234567890',
    nonce: 'test_nonce',
    signature: 'wrong_signature'
  },
  {
    name: '时间戳过期测试',
    timestamp: '1000000000', // 很久以前的时间戳
    nonce: 'test_nonce',
    generateCorrectSignature: true
  }
];

testCases.forEach((testCase, index) => {
  console.log(`\n   测试 ${index + 1}: ${testCase.name}`);
  
  let signature = testCase.signature;
  if (testCase.generateCorrectSignature) {
    signature = CryptoUtil.generateSignature(
      config.wechat.token,
      testCase.timestamp,
      testCase.nonce
    );
  }
  
  const validation = CryptoUtil.validateWeChatMessage({
    signature,
    timestamp: testCase.timestamp,
    nonce: testCase.nonce,
    token: config.wechat.token
  });
  
  console.log(`   参数: timestamp=${testCase.timestamp}, nonce=${testCase.nonce}`);
  console.log(`   签名: ${signature}`);
  console.log(`   结果: ${validation.valid ? '✅ 通过' : '❌ 失败'}`);
  if (!validation.valid && validation.error) {
    console.log(`   错误: ${validation.error}`);
  }
  if (validation.timeDiff !== undefined) {
    console.log(`   时间差: ${validation.timeDiff}秒`);
  }
});

// 生成微信验证URL示例
console.log('\n🌐 微信公众号配置信息:');
console.log('   在微信公众平台配置以下信息:');
console.log('   URL: https://your-domain.com/wechat');
console.log(`   Token: ${config.wechat.token}`);
console.log('   EncodingAESKey: 随机生成43位字符');
console.log('   消息加解密方式: 明文模式\n');

// 测试步骤说明
console.log('📝 测试步骤:');
console.log('1. 启动服务: npm start');
console.log('2. 使用ngrok获取公网URL: ngrok http 3001');
console.log('3. 在微信公众平台配置服务器URL');
console.log('4. 点击"提交"进行验证');
console.log('5. 验证通过后即可接收微信消息\n');

// 生成测试用的验证参数
console.log('🔗 测试验证参数生成:');
const testTimestamp = Math.floor(Date.now() / 1000).toString();
const testNonce = CryptoUtil.generateNonce();
const testSignature = CryptoUtil.generateSignature(
  config.wechat.token,
  testTimestamp,
  testNonce
);

console.log(`   timestamp: ${testTimestamp}`);
console.log(`   nonce: ${testNonce}`);
console.log(`   signature: ${testSignature}`);
console.log(`   验证URL: http://localhost:${config.server.port}/wechat?signature=${testSignature}&timestamp=${testTimestamp}&nonce=${testNonce}&echostr=test_echo\n`);

// API测试建议
console.log('🧪 API测试建议:');
console.log(`   健康检查: curl http://localhost:${config.server.port}/health`);
console.log(`   配置查看: curl http://localhost:${config.server.port}/wechat/config`);
console.log(`   签名测试: curl "http://localhost:${config.server.port}/wechat/test?action=signature&timestamp=${testTimestamp}&nonce=${testNonce}&signature=${testSignature}"`);
console.log(`   消息测试: curl http://localhost:${config.server.port}/wechat/test?action=message\n`);

console.log('✨ 测试完成！');
